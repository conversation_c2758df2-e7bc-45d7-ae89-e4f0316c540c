#!/bin/bash

SCRIPT_DIR="/home/<USER>/can_uploader"

echo "=== CAN Logger 增强版安装脚本 ==="
echo "此脚本将安装带有完整日志记录功能的 CAN Logger 增强版"
echo ""

# === 拷贝文件 ===
echo "[1/5] 拷贝文件到目标目录..."
sudo mkdir -p $SCRIPT_DIR
sudo cp can_logger_enhanced.py can_config.yaml ota_update.sh clean_log.sh $SCRIPT_DIR
echo "[✓] 文件拷贝完成"

# === 安装依赖 ===
echo "[2/5] 安装Python依赖..."
pip3 install python-can requests pyyaml
echo "[✓] 依赖安装完成"

# === 安装服务文件 ===
echo "[3/5] 安装systemd服务文件..."
sudo cp can-logger-enhanced.service /etc/systemd/system/can-logger-enhanced.service
sudo chmod +x $SCRIPT_DIR/*.sh
echo "[✓] 服务文件安装完成"

# === 注册定时清理任务 ===
echo "[4/5] 添加日志清理定时任务..."
# 检查是否已存在清理任务
if crontab -l 2>/dev/null | grep -q "clean_log.sh"; then
    echo "[!] 日志清理任务已存在，跳过添加"
else
    (crontab -l 2>/dev/null; echo "0 */6 * * * bash $SCRIPT_DIR/clean_log.sh") | crontab -
    echo "[✓] 日志清理任务添加完成"
fi

# === 启动服务 ===
echo "[5/5] 启动CAN Logger增强版服务..."
sudo systemctl daemon-reload
sudo systemctl enable can-logger-enhanced
sudo systemctl restart can-logger-enhanced

# 等待服务启动
sleep 3

# 检查服务状态
if sudo systemctl is-active --quiet can-logger-enhanced; then
    echo "[✓] CAN Logger 增强版服务启动成功"
else
    echo "[✗] CAN Logger 增强版服务启动失败"
    echo "请检查服务状态: sudo systemctl status can-logger-enhanced"
    exit 1
fi

echo ""
echo "=== 安装完成 ==="
echo "服务名称: can-logger-enhanced"
echo "主程序: $SCRIPT_DIR/can_logger_enhanced.py"
echo "服务日志: $SCRIPT_DIR/can_service.log"
echo "错误日志: $SCRIPT_DIR/can_error.log"
echo ""
echo "常用命令:"
echo "  查看服务状态: sudo systemctl status can-logger-enhanced"
echo "  查看实时日志: journalctl -fu can-logger-enhanced"
echo "  查看服务日志: tail -f $SCRIPT_DIR/can_service.log"
echo "  查看错误日志: tail -f $SCRIPT_DIR/can_error.log"
echo "  停止服务:     sudo systemctl stop can-logger-enhanced"
echo "  启动服务:     sudo systemctl start can-logger-enhanced"
echo "  重启服务:     sudo systemctl restart can-logger-enhanced"
