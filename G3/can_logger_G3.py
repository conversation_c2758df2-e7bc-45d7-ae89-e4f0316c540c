
import can
import yaml
import time
import threading
import requests
import os
import logging
from datetime import datetime

CONFIG_PATH = "can_config.yaml"
ERROR_LOG = "can_error.log"

# 加载配置
with open(CONFIG_PATH, 'r') as f:
    config = yaml.safe_load(f)

CAN_INTERFACE = config.get("can_interface", "can0")
BITRATE = config.get("bitrate", 250000)
CAN_ID_LIST = set([int(i, 16) if isinstance(i, str) else i for i in config.get("can_id_list", [])])
UPLOAD_INTERVAL = config.get("upload_interval", 10)
UPLOAD_URL = config.get("upload_url", "")
LOG_PATH = config.get("local_log_path", "can_data_backup.json")
ENABLE_LOCAL_LOG = config.get("enable_local_log", True)
VIN = config.get("vin", "UNKNOWN")

# 错误限频控制器
class ErrorRateLimiter:
    def __init__(self):
        self.last_error = ""
        self.count = 0
        self.last_log_time = 0

    def should_log(self, msg):
        now = time.time()
        if msg != self.last_error:
            self.last_error = msg
            self.count = 1
            self.last_log_time = now
            return True
        else:
            wait_time = min(60 * (2 ** self.count), 1800)
            if now - self.last_log_time >= wait_time:
                self.count += 1
                self.last_log_time = now
                return True
            return False

error_limiter = ErrorRateLimiter()

class CanListener(can.Listener):
    def on_message_received(self, msg):
        if msg.arbitration_id in CAN_ID_LIST:
            data_str = ' '.join(f"{b:02x}" for b in msg.data).upper()
            can_data_cache[f"{msg.arbitration_id}"] = data_str

# 初始化日志器
logging.basicConfig(
    filename=ERROR_LOG,
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 初始化缓存字典
can_data_cache = {}  # {hex(can_id): "xx xx xx xx ..."}

def setup_can_interface():
    force = config.get("force_init_can", False)

    # 检查接口状态
    try:
        result = os.popen(f"ip link show {CAN_INTERFACE}").read()
        if "state UP" in result:
            print(f"[✓] {CAN_INTERFACE} 已启动")
            return
        elif not force and "state DOWN" in result:
            print(f"[!] {CAN_INTERFACE} 已存在但未启动，尝试启动...")
            # 只启动接口，不重新配置
            ret = os.system(f"ifconfig {CAN_INTERFACE} up")
            if ret == 0:
                print(f"[✓] {CAN_INTERFACE} 启动成功")
                return
    except Exception as e:
        print(f"[!] 检查接口状态时出错: {e}")

    if force:
        print(f"[!] 强制重新配置 {CAN_INTERFACE} ...")
        # 先关闭接口
        os.system(f"ifconfig {CAN_INTERFACE} down 2>/dev/null")
        # 重新配置
        ret1 = os.system(f"ip link set {CAN_INTERFACE} type can bitrate {BITRATE}")
        ret2 = os.system(f"ifconfig {CAN_INTERFACE} up")
        if ret1 == 0 and ret2 == 0:
            print(f"[✓] {CAN_INTERFACE} 强制重新配置完成")
        else:
            print(f"[!] {CAN_INTERFACE} 配置可能失败，但继续运行...")
    else:
        print(f"[!] 初始化 {CAN_INTERFACE} ...")
        ret1 = os.system(f"ip link set {CAN_INTERFACE} type can bitrate {BITRATE}")
        ret2 = os.system(f"ifconfig {CAN_INTERFACE} up")
        if ret1 == 0 and ret2 == 0:
            print(f"[✓] {CAN_INTERFACE} 初始化完成")
        else:
            print(f"[!] {CAN_INTERFACE} 配置可能失败，但继续运行...")



def uploader():
    while True:
        time.sleep(UPLOAD_INTERVAL)
        try:
            payload = {
                "vin": VIN,
                "time": int(time.time() * 1000)
            }
            payload.update(can_data_cache)
            res = requests.post(UPLOAD_URL, json=payload, timeout=5)
            if res.status_code != 200:
                raise Exception(f"Upload failed: {res.status_code}")
            if ENABLE_LOCAL_LOG:
                with open(LOG_PATH, 'a') as f:
                    f.write(str(payload) + "\n")
        except Exception as e:
            msg = f"[UPLOAD ERROR] {e}"
            if error_limiter.should_log(str(e)):
                print(msg)
                logging.error(msg)

def main():
    setup_can_interface()
    bus = can.interface.Bus(channel=CAN_INTERFACE, bustype='socketcan')
    listener = CanListener()
    notifier = can.Notifier(bus, [listener])

    upload_thread = threading.Thread(target=uploader, daemon=True)
    upload_thread.start()

    while True:
        time.sleep(60)

if __name__ == "__main__":
    main()
