#!/bin/bash

# CAN Logger 启动脚本 (不依赖systemctl)
# 适用于无法使用systemd的Linux系统

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/can_logger_simple.py"
PID_FILE="$SCRIPT_DIR/can_logger.pid"
LOG_FILE="$SCRIPT_DIR/can_logger_output.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python脚本是否存在
check_script() {
    if [ ! -f "$PYTHON_SCRIPT" ]; then
        print_error "Python脚本不存在: $PYTHON_SCRIPT"
        exit 1
    fi
}

# 检查是否已经运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 正在运行
        else
            rm -f "$PID_FILE"  # 清理无效的PID文件
            return 1  # 未运行
        fi
    fi
    return 1  # 未运行
}

# 启动服务
start_service() {
    check_script
    
    if is_running; then
        print_warning "CAN Logger 已经在运行中"
        return 0
    fi
    
    print_status "启动 CAN Logger..."
    
    # 切换到脚本目录
    cd "$SCRIPT_DIR"
    
    # 后台启动Python脚本
    nohup python3 "$PYTHON_SCRIPT" > "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$PID_FILE"
    
    # 等待一下检查是否启动成功
    sleep 2
    if ps -p "$pid" > /dev/null 2>&1; then
        print_status "CAN Logger 启动成功 (PID: $pid)"
        print_status "日志文件: $LOG_FILE"
    else
        print_error "CAN Logger 启动失败"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服务
stop_service() {
    if ! is_running; then
        print_warning "CAN Logger 未运行"
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    print_status "停止 CAN Logger (PID: $pid)..."
    
    # 发送TERM信号
    kill -TERM "$pid" 2>/dev/null
    
    # 等待进程结束
    local count=0
    while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 10 ]; do
        sleep 1
        count=$((count + 1))
    done
    
    # 如果还在运行，强制杀死
    if ps -p "$pid" > /dev/null 2>&1; then
        print_warning "强制停止进程..."
        kill -KILL "$pid" 2>/dev/null
    fi
    
    rm -f "$PID_FILE"
    print_status "CAN Logger 已停止"
}

# 重启服务
restart_service() {
    print_status "重启 CAN Logger..."
    stop_service
    sleep 1
    start_service
}

# 查看状态
show_status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        print_status "CAN Logger 正在运行 (PID: $pid)"
        
        # 显示进程信息
        echo "进程信息:"
        ps -p "$pid" -o pid,ppid,cmd,etime
        
        # 显示最近的日志
        if [ -f "$LOG_FILE" ]; then
            echo ""
            echo "最近的日志 (最后10行):"
            tail -10 "$LOG_FILE"
        fi
    else
        print_warning "CAN Logger 未运行"
    fi
}

# 查看日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        if [ "$1" = "-f" ]; then
            print_status "实时查看日志 (Ctrl+C 退出):"
            tail -f "$LOG_FILE"
        else
            print_status "显示日志 (最后50行):"
            tail -50 "$LOG_FILE"
        fi
    else
        print_warning "日志文件不存在: $LOG_FILE"
    fi
}

# 主函数
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs [-f]}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动 CAN Logger"
        echo "  stop    - 停止 CAN Logger"
        echo "  restart - 重启 CAN Logger"
        echo "  status  - 查看运行状态"
        echo "  logs    - 查看日志 (加 -f 参数实时查看)"
        echo ""
        echo "示例:"
        echo "  $0 start"
        echo "  $0 logs -f"
        exit 1
        ;;
esac
