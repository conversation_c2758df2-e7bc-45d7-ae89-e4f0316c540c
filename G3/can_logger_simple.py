#!/usr/bin/env python3

import can
import time
import threading
import requests
import os
import logging
from datetime import datetime

CONFIG_PATH = "can_config_simple.conf"
ERROR_LOG = "can_error.log"

def load_config():
    """加载配置文件，不依赖yaml"""
    config = {}
    try:
        with open(CONFIG_PATH, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # 处理不同类型的值
                        if value.lower() in ('true', 'false'):
                            config[key] = value.lower() == 'true'
                        elif value.isdigit():
                            config[key] = int(value)
                        else:
                            config[key] = value
        return config
    except Exception as e:
        print(f"[!] 加载配置文件失败: {e}")
        return {}

# 加载配置
config = load_config()

# 配置参数
CAN_INTERFACE = config.get("CAN_INTERFACE", "can0")
BITRATE = config.get("BITRATE", 250000)
UPLOAD_INTERVAL = config.get("UPLOAD_INTERVAL", 10)
UPLOAD_URL = config.get("UPLOAD_URL", "")
LOG_PATH = config.get("LOCAL_LOG_PATH", "can_data_backup.json")
ENABLE_LOCAL_LOG = config.get("ENABLE_LOCAL_LOG", True)
VIN = config.get("VIN", "UNKNOWN")
FORCE_INIT_CAN = config.get("FORCE_INIT_CAN", False)

# 处理CAN ID列表
can_id_str = config.get("CAN_ID_LIST", "")
if can_id_str:
    CAN_ID_LIST = set([int(i.strip()) for i in can_id_str.split(',') if i.strip()])
else:
    CAN_ID_LIST = set()

print(f"[INFO] 加载了 {len(CAN_ID_LIST)} 个CAN ID")

# 错误限频控制器
class ErrorRateLimiter:
    def __init__(self):
        self.last_error = ""
        self.count = 0
        self.last_log_time = 0

    def should_log(self, msg):
        now = time.time()
        if msg != self.last_error:
            self.last_error = msg
            self.count = 1
            self.last_log_time = now
            return True
        else:
            wait_time = min(60 * (2 ** self.count), 1800)
            if now - self.last_log_time >= wait_time:
                self.count += 1
                self.last_log_time = now
                return True
            return False

error_limiter = ErrorRateLimiter()

class CanListener(can.Listener):
    def on_message_received(self, msg):
        if msg.arbitration_id in CAN_ID_LIST:
            data_str = ' '.join(f"{b:02x}" for b in msg.data).upper()
            can_data_cache[f"{msg.arbitration_id}"] = data_str

# 初始化日志器
logging.basicConfig(
    filename=ERROR_LOG,
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 初始化缓存字典
can_data_cache = {}  # {hex(can_id): "xx xx xx xx ..."}

def setup_can_interface():
    """设置CAN接口"""
    # 检查接口状态
    try:
        result = os.popen(f"ip link show {CAN_INTERFACE}").read()
        
        if "state DOWN" in result:  # 情况1: state DOWN => 设置bitrate 250000并启动
            print(f"[!] {CAN_INTERFACE} 处于DOWN状态，开始配置...")
            _configure_and_start_interface("初始化")
            return
            
        elif "state UP" in result: # 情况2: state UP => 需要进一步检查物理链路状态
            if "LOWER_UP" in result: # 情况2.1: 既有UP又有LOWER_UP，物理链路正常
                print(f"[✓] {CAN_INTERFACE} 已正常启动")
                return
            else: # 情况2.2: 只有UP没有LOWER_UP，物理链路有问题
                print(f"[!] {CAN_INTERFACE} 物理链路异常 (UP但无LOWER_UP)，重新配置...")
                # 先down，再重新配置
                os.system(f"ifconfig {CAN_INTERFACE} down 2>/dev/null")
                _configure_and_start_interface("重新配置")
                return
        
        # 如果强制重新配置
        if FORCE_INIT_CAN:
            print(f"[!] 强制重新配置 {CAN_INTERFACE} ...")
            os.system(f"ifconfig {CAN_INTERFACE} down 2>/dev/null")
            _configure_and_start_interface("强制重新配置")
            return
            
    except Exception as e:
        print(f"[!] 检查接口状态时出错: {e}")
    
    # 默认情况：初始化接口
    print(f"[!] 初始化 {CAN_INTERFACE} ...")
    _configure_and_start_interface("初始化")

def _configure_and_start_interface(action_name):
    """配置并启动CAN接口的通用函数"""
    ret1 = os.system(f"ip link set {CAN_INTERFACE} type can bitrate {BITRATE}")
    ret2 = os.system(f"ifconfig {CAN_INTERFACE} up")
    
    if ret1 == 0 and ret2 == 0:
        print(f"[✓] {CAN_INTERFACE} {action_name}完成")
    else:
        print(f"[!] {CAN_INTERFACE} {action_name}可能失败，但继续运行...")

def uploader():
    """数据上传线程"""
    while True:
        time.sleep(UPLOAD_INTERVAL)
        try:
            payload = {
                "vin": VIN,
                "time": int(time.time() * 1000)
            }
            payload.update(can_data_cache)
            res = requests.post(UPLOAD_URL, json=payload, timeout=5)
            if res.status_code != 200:
                raise Exception(f"Upload failed: {res.status_code}")
            if ENABLE_LOCAL_LOG:
                with open(LOG_PATH, 'a') as f:
                    f.write(str(payload) + "\n")
        except Exception as e:
            msg = f"[UPLOAD ERROR] {e}"
            if error_limiter.should_log(str(e)):
                print(msg)
                logging.error(msg)

def main():
    print(f"[INFO] 启动CAN Logger - VIN: {VIN}")
    print(f"[INFO] 配置: {CAN_INTERFACE}@{BITRATE}, 上传间隔: {UPLOAD_INTERVAL}s")
    
    setup_can_interface()
    bus = can.interface.Bus(channel=CAN_INTERFACE, bustype='socketcan')
    listener = CanListener()
    notifier = can.Notifier(bus, [listener])

    upload_thread = threading.Thread(target=uploader, daemon=True)
    upload_thread.start()

    print(f"[✓] CAN Logger 启动完成，开始监听...")
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        print(f"\n[INFO] 收到停止信号，正在关闭...")
        notifier.stop()
        bus.shutdown()
        print(f"[✓] CAN Logger 已停止")

if __name__ == "__main__":
    main()
