ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
ERROR:root:[UPLOAD ERROR] HTTPConnectionPool(host='***************', port=7001): Read timed out. (read timeout=5)
