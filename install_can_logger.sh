#!/bin/bash

SCRIPT_DIR="/home/<USER>/can_uploader"

# === 拷贝文件 ===
sudo mkdir -p $SCRIPT_DIR
sudo cp can_logger_with_config.py can_config.yaml ota_update.sh clean_log.sh $SCRIPT_DIR

# === 设置权限 ===
sudo chown -R ubuntu:ubuntu $SCRIPT_DIR
sudo chmod -R 755 $SCRIPT_DIR

# === 预创建日志文件 ===
sudo touch $SCRIPT_DIR/can_error.log
sudo touch $SCRIPT_DIR/can_data_backup.json
sudo chown ubuntu:ubuntu $SCRIPT_DIR/*.log $SCRIPT_DIR/*.json 2>/dev/null || true
sudo chmod 664 $SCRIPT_DIR/*.log $SCRIPT_DIR/*.json 2>/dev/null || true

# === 安装依赖 ===
pip3 install python-can requests pyyaml

# === 安装服务文件 ===
sudo cp can-logger.service /etc/systemd/system/can-logger.service
sudo chmod +x $SCRIPT_DIR/*.sh

# === 注册定时清理任务 ===
echo "[+] 添加日志清理定时任务..."
(crontab -l 2>/dev/null; echo "0 */6 * * * bash $SCRIPT_DIR/clean_log.sh") | crontab -

# === 启动服务 ===
sudo systemctl daemon-reexec
sudo systemctl daemon-reload
sudo systemctl enable can-logger
sudo systemctl restart can-logger

echo "[✓] 安装完成。使用 'journalctl -fu can-logger' 查看日志"